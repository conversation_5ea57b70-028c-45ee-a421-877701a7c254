/* 全局主题样式 */

/* 主色调 */
:root {
  --primary-color: #3498db;
  --secondary-color: #2c3e50;
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --danger-color: #e74c3c;
  --info-color: #17a2b8;
  --light-color: #ecf0f1;
  --dark-color: #2c3e50;
  
  /* 渐变色 */
  --header-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --main-gradient: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  --sidebar-color: #2c3e50;
  --sidebar-hover: #34495e;
  
  /* 阴影 */
  --box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  
  /* 边框圆角 */
  --border-radius: 8px;
  --border-radius-small: 4px;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

/* 卡片样式 */
.card {
  background: #ffffff;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: var(--card-shadow);
    transform: translateY(-2px);
  }
}

/* 按钮样式增强 */
.btn-modern {
  padding: 10px 20px;
  border-radius: var(--border-radius-small);
  border: none;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  
  &.btn-primary {
    background: var(--primary-color);
    color: white;
    
    &:hover {
      background: #5dade2;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
    }
  }
  
  &.btn-success {
    background: var(--success-color);
    color: white;
    
    &:hover {
      background: #2ecc71;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
    }
  }
  
  &.btn-danger {
    background: var(--danger-color);
    color: white;
    
    &:hover {
      background: #ec7063;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
    }
  }
}

/* 表格样式增强 */
.table-modern {
  .el-table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    
    .el-table__header {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      
      th {
        background: transparent !important;
        color: var(--dark-color);
        font-weight: 600;
        border-bottom: 2px solid var(--primary-color);
      }
    }
    
    .el-table__row {
      transition: all 0.3s ease;
      
      &:hover {
        background-color: rgba(52, 152, 219, 0.05) !important;
      }
    }
  }
}

/* 表单样式增强 */
.form-modern {
  .el-form-item {
    margin-bottom: var(--spacing-lg);
    
    .el-form-item__label {
      color: var(--dark-color);
      font-weight: 500;
    }
    
    .el-input__inner,
    .el-textarea__inner,
    .el-select .el-input__inner {
      border-radius: var(--border-radius-small);
      border: 1px solid #ddd;
      transition: all 0.3s ease;
      
      &:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
      }
    }
  }
}

/* 分页样式增强 */
.pagination-modern {
  .el-pagination {
    .el-pager li {
      border-radius: var(--border-radius-small);
      margin: 0 2px;
      transition: all 0.3s ease;
      
      &.active {
        background: var(--primary-color);
        color: white;
      }
      
      &:hover {
        background: var(--primary-color);
        color: white;
      }
    }
    
    .btn-prev,
    .btn-next {
      border-radius: var(--border-radius-small);
      transition: all 0.3s ease;
      
      &:hover {
        background: var(--primary-color);
        color: white;
      }
    }
  }
}

/* 面包屑样式 */
.breadcrumb-modern {
  background: rgba(255, 255, 255, 0.9);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  backdrop-filter: blur(10px);
  
  .el-breadcrumb__item {
    .el-breadcrumb__inner {
      color: var(--dark-color);
      font-weight: 500;
      
      &:hover {
        color: var(--primary-color);
      }
    }
  }
}

/* 加载动画 */
.loading-modern {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-xl);
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
  }
  
  .btn-modern {
    padding: 8px 16px;
    font-size: 12px;
  }
}
