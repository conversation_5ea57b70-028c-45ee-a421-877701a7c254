/* 全局list页面按钮样式 */
.slt {
    margin: 0 !important;
    display: flex;
}

.ad {
    margin: 0 !important;
    display: flex;
}

.pages {
& /deep/ el-pagination__sizes{
& /deep/ el-input__inner {
      height: 22px;
      line-height: 22px;
  }
}
}


.el-button+.el-button {
    margin:0;
}

.tables {
& /deep/ .el-button--success {
      height: 32px;
      color: #ffffff;
      font-size: 12px;
      border-width: 1px;
      border-style: solid;
      border-color: #27ae60;
      border-radius: 4px;
      background-color: #27ae60;
      transition: all 0.3s ease;

      &:hover {
          background-color: #2ecc71;
          border-color: #2ecc71;
      }
  }

& /deep/ .el-button--primary {
      height: 32px;
      color: #ffffff;
      font-size: 12px;
      border-width: 1px;
      border-style: solid;
      border-color: #3498db;
      border-radius: 4px;
      background-color: #3498db;
      transition: all 0.3s ease;

      &:hover {
          background-color: #5dade2;
          border-color: #5dade2;
      }
  }

& /deep/ .el-button--danger {
      height: 32px;
      color: #ffffff;
      font-size: 12px;
      border-width: 1px;
      border-style: solid;
      border-color: #e74c3c;
      border-radius: 4px;
      background-color: #e74c3c;
      transition: all 0.3s ease;

      &:hover {
          background-color: #ec7063;
          border-color: #ec7063;
      }
  }

& /deep/ .el-button {
      margin: 4px;
      font-weight: 500;
  }
}

/* 全局add-or-update页面按钮样式 */
.editor{
    height: 500px;

& /deep/ .ql-container {
      height: 310px;
  }
}
.amap-wrapper {
    width: 100%;
    height: 500px;
}
.search-box {
    position: absolute;
}
.addEdit-block {
    margin: -10px;
}
.detail-form-content {
    padding: 12px;
}
.btn .el-button {
    padding: 0;
}
/*IndexMain.vue页面 list页面样式
	//背景颜色
		.el-main
	//list页面的边框颜色
		.router-view
*/